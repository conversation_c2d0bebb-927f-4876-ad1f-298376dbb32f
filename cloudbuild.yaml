steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--tag'
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-staging:${BRANCH_NAME}
      - '--file'
      - ./Dockerfile.app
      - .
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-staging:${BRANCH_NAME}
images:
  - >-
    ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-staging:${BRANCH_NAME}
options:
  substitutionOption: ALLOW_LOOSE
  logging: CLOUD_LOGGING_ONLY
